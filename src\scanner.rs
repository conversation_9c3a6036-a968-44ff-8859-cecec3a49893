use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use std::path::{Path, PathBuf};
use std::time::{Duration, SystemTime};
use tokio::sync::mpsc;
use walkdir::WalkDir;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioFile {
    pub path: PathBuf,
    pub title: Option<String>,
    pub artist: Option<String>,
    pub album: Option<String>,
    pub genre: Option<String>,
    pub duration: Option<Duration>,
    pub track_number: Option<u32>,
    pub year: Option<u32>,
    pub file_size: u64,
    pub last_modified: SystemTime,
}

#[derive(Debug, Clone)]
pub enum ScanEvent {
    Started,
    Progress { current: usize, total: usize },
    FileFound(AudioFile),
    Completed { total_files: usize },
    Error(String),
}

#[derive(Clone)]
pub struct AudioScanner {
    supported_extensions: HashSet<String>,
}

impl AudioScanner {
    pub fn new() -> Self {
        let mut supported_extensions = HashSet::new();
        
        // Add all supported audio formats
        supported_extensions.insert("mp3".to_string());
        supported_extensions.insert("flac".to_string());
        supported_extensions.insert("wav".to_string());
        supported_extensions.insert("ogg".to_string());
        supported_extensions.insert("m4a".to_string());
        supported_extensions.insert("aac".to_string());
        supported_extensions.insert("wma".to_string());
        supported_extensions.insert("opus".to_string());
        supported_extensions.insert("aiff".to_string());
        supported_extensions.insert("ape".to_string());
        
        Self {
            supported_extensions,
        }
    }
    
    pub async fn scan_system(&self, progress_tx: mpsc::UnboundedSender<ScanEvent>) -> Result<Vec<AudioFile>> {
        let _ = progress_tx.send(ScanEvent::Started);
        
        let mut audio_files = Vec::new();
        let mut total_processed = 0;
        
        // Get all drives/mount points to scan
        let scan_paths = self.get_scan_paths()?;
        
        for scan_path in scan_paths {
            if let Err(e) = self.scan_directory(&scan_path, &mut audio_files, &mut total_processed, &progress_tx).await {
                let _ = progress_tx.send(ScanEvent::Error(format!("Error scanning {}: {}", scan_path.display(), e)));
            }
        }
        
        let _ = progress_tx.send(ScanEvent::Completed { total_files: audio_files.len() });
        Ok(audio_files)
    }
    
    async fn scan_directory(
        &self,
        path: &Path,
        audio_files: &mut Vec<AudioFile>,
        total_processed: &mut usize,
        progress_tx: &mpsc::UnboundedSender<ScanEvent>,
    ) -> Result<()> {
        let walker = WalkDir::new(path)
            .follow_links(false)
            .max_depth(20) // Reasonable depth limit
            .into_iter();
            
        for entry in walker.filter_map(|e| e.ok()) {
            let path = entry.path();
            
            // Skip if not a file
            if !path.is_file() {
                continue;
            }
            
            // Check if it's an audio file
            if let Some(extension) = path.extension() {
                if let Some(ext_str) = extension.to_str() {
                    if self.supported_extensions.contains(&ext_str.to_lowercase()) {
                        match self.extract_metadata(path).await {
                            Ok(audio_file) => {
                                let _ = progress_tx.send(ScanEvent::FileFound(audio_file.clone()));
                                audio_files.push(audio_file);
                            }
                            Err(e) => {
                                let _ = progress_tx.send(ScanEvent::Error(format!("Error reading {}: {}", path.display(), e)));
                            }
                        }
                    }
                }
            }
            
            *total_processed += 1;
            
            // Send progress updates every 100 files
            if *total_processed % 100 == 0 {
                let _ = progress_tx.send(ScanEvent::Progress {
                    current: *total_processed,
                    total: 0, // We don't know total in advance
                });
            }
            
            // Yield control to allow other tasks to run
            if *total_processed % 10 == 0 {
                tokio::task::yield_now().await;
            }
        }
        
        Ok(())
    }
    
    async fn extract_metadata(&self, path: &Path) -> Result<AudioFile> {
        let metadata = std::fs::metadata(path)?;
        let file_size = metadata.len();
        let last_modified = metadata.modified()?;
        
        // For now, we'll create a basic AudioFile with just file info
        // In a full implementation, we'd use symphonia to extract audio metadata
        let filename = path.file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("Unknown")
            .to_string();
            
        Ok(AudioFile {
            path: path.to_path_buf(),
            title: Some(filename),
            artist: None,
            album: None,
            genre: None,
            duration: None,
            track_number: None,
            year: None,
            file_size,
            last_modified,
        })
    }
    
    fn get_scan_paths(&self) -> Result<Vec<PathBuf>> {
        let mut paths = Vec::new();
        
        #[cfg(windows)]
        {
            // On Windows, scan all available drives
            for drive_letter in b'A'..=b'Z' {
                let drive_path = format!("{}:\\", drive_letter as char);
                let path = PathBuf::from(&drive_path);
                if path.exists() {
                    paths.push(path);
                }
            }
        }
        
        #[cfg(unix)]
        {
            // On Unix-like systems, start from root and common user directories
            paths.push(PathBuf::from("/"));
            
            if let Ok(home) = std::env::var("HOME") {
                paths.push(PathBuf::from(home));
            }
        }
        
        // If no paths found, default to current directory
        if paths.is_empty() {
            paths.push(PathBuf::from("."));
        }
        
        Ok(paths)
    }
    
    pub fn is_audio_file(&self, path: &Path) -> bool {
        if let Some(extension) = path.extension() {
            if let Some(ext_str) = extension.to_str() {
                return self.supported_extensions.contains(&ext_str.to_lowercase());
            }
        }
        false
    }
}
