# Echo Music Player

A high-performance CLI music player built in Rust with 120Hz refresh rate, full system audio file scanning, and keyboard-only navigation.

## Features

- **120Hz Refresh Rate**: Ultra-smooth terminal UI with 8.33ms frame time
- **Full System Scan**: Automatically discovers all audio files across your entire PC
- **Keyboard-Only Navigation**: Complete control without mouse dependency
- **Multiple Audio Formats**: Supports MP3, FLAC, WAV, OGG, M4A, AAC, WMA, OPUS, AIFF, APE
- **Real-time Audio Playback**: Powered by Rodio audio engine
- **Modern Terminal UI**: Built with Ratat<PERSON> for responsive interface

## Supported Audio Formats

- MP3
- FLAC
- WAV
- OGG
- M4A
- AAC
- WMA
- OPUS
- AIFF
- APE

## Installation

### Prerequisites

- Rust 1.70+ (2024 edition)
- Windows, macOS, or Linux

### Build from Source

```bash
git clone <repository-url>
cd echo-player
cargo build --release
```

### Run

```bash
cargo run
```

## Controls

### Navigation
- `↑` / `k` - Navigate up
- `↓` / `j` - Navigate down
- `←` / `h` - Navigate left
- `→` / `l` - Navigate right
- `Enter` - Select item
- `Tab` - Switch between views

### Playback
- `Space` - Play/Pause
- `s` - Stop
- `n` - Next track
- `b` - Previous track
- `+` / `=` - Volume up
- `-` - Volume down

### System
- `F5` - Scan for music files
- `q` / `Esc` - Quit

## Architecture

### Core Components

1. **Audio Engine** (`src/audio.rs`)
   - Handles audio playback using Rodio
   - Supports play, pause, stop, volume control
   - Asynchronous command processing

2. **File Scanner** (`src/scanner.rs`)
   - Recursively scans entire file system
   - Identifies audio files by extension
   - Extracts metadata (future enhancement)

3. **Terminal UI** (`src/ui.rs`)
   - 120Hz refresh rate terminal interface
   - Multiple view states (Library, Player, Scanning)
   - Keyboard event handling

4. **Music Library** (`src/library.rs`)
   - Organizes audio files by artist, album, genre
   - Search functionality
   - Indexing for fast access

5. **Player Controller** (`src/player.rs`)
   - Coordinates between UI, audio engine, and library
   - Manages application state
   - Handles user interactions

## Performance Features

### 120Hz Refresh Rate
The UI maintains a consistent 120Hz refresh rate (8.33ms per frame) for ultra-smooth visual feedback:

```rust
let frame_duration = Duration::from_millis(8); // 120Hz
```

### Asynchronous Architecture
- Non-blocking audio processing
- Background file scanning
- Responsive UI during heavy operations

### Memory Efficiency
- Streaming audio playback
- Lazy loading of metadata
- Efficient file indexing

## Development

### Project Structure

```
src/
├── main.rs          # Application entry point
├── lib.rs           # Module exports
├── audio.rs         # Audio engine
├── scanner.rs       # File system scanner
├── ui.rs            # Terminal UI
├── library.rs       # Music library management
├── player.rs        # Main player controller
└── config.rs        # Configuration management
```

### Dependencies

- **rodio**: Audio playback engine
- **ratatui**: Terminal UI framework
- **crossterm**: Cross-platform terminal manipulation
- **tokio**: Async runtime
- **walkdir**: File system traversal
- **serde**: Serialization
- **anyhow**: Error handling

### Building

```bash
# Debug build
cargo build

# Release build (optimized)
cargo build --release

# Run with logging
RUST_LOG=debug cargo run
```

## Future Enhancements

- [ ] Playlist management
- [ ] Audio metadata extraction using Symphonia
- [ ] Equalizer controls
- [ ] Gapless playback
- [ ] Last.fm scrobbling
- [ ] Configuration file support
- [ ] Custom keybindings
- [ ] Search functionality
- [ ] Album art display (ASCII)
- [ ] Crossfade between tracks

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with [Rodio](https://github.com/RustAudio/rodio) for audio playback
- UI powered by [Ratatui](https://github.com/ratatui-org/ratatui)
- Cross-platform terminal support via [Crossterm](https://github.com/crossterm-rs/crossterm)
