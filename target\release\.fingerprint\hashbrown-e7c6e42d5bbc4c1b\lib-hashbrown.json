{"rustc": 10895048813736897673, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2040997289075261528, "path": 16783973399325666972, "deps": [[5230392855116717286, "equivalent", false, 2976578131810645051], [9150530836556604396, "allocator_api2", false, 7077338230636370169], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 6657317077242849289]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hashbrown-e7c6e42d5bbc4c1b\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}