{"rustc": 10895048813736897673, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 2241668132362809309, "path": 6386442710890725783, "deps": [[2828590642173593838, "cfg_if", false, 10203312834218750424]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\encoding_rs-8bcdfc83cf05e758\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}