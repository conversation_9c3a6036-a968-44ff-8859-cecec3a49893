{"rustc": 10895048813736897673, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 187265481308423917, "path": 14902460355667300587, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\allocator-api2-5b078bb505788506\\dep-lib-allocator_api2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}