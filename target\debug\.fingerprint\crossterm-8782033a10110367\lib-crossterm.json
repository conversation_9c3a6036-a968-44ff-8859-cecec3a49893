{"rustc": 10895048813736897673, "features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"events\", \"windows\"]", "declared_features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"event-stream\", \"events\", \"filedescriptor\", \"libc\", \"osc52\", \"serde\", \"use-dev-tty\", \"windows\"]", "target": 7162149947039624270, "profile": 15657897354478470176, "path": 427690204101721828, "deps": [[4495526598637097934, "parking_lot", false, 13926615418311304999], [7896293946984509699, "bitflags", false, 16097543648619965667], [10020888071089587331, "<PERSON>ap<PERSON>", false, 14709893585410223817], [11293676373856528358, "derive_more", false, 7669516594724479338], [11763018104473073732, "document_features", false, 665916809547510706], [17658759660230624279, "crossterm_winapi", false, 7996025956608407147]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\crossterm-8782033a10110367\\dep-lib-crossterm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}