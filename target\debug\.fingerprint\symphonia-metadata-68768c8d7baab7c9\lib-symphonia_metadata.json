{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 2684992013079232362, "profile": 15657897354478470176, "path": 18181702015257684710, "deps": [[1218881066841546592, "symphonia_core", false, 1772284853577580862], [5986029879202738730, "log", false, 11226432392582311476], [14564311161534545801, "encoding_rs", false, 15122673206892199876], [17917672826516349275, "lazy_static", false, 4862664117436432470]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\symphonia-metadata-68768c8d7baab7c9\\dep-lib-symphonia_metadata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}