{"rustc": 10895048813736897673, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2241668132362809309, "path": 16783973399325666972, "deps": [[5230392855116717286, "equivalent", false, 3083290908061137457], [9150530836556604396, "allocator_api2", false, 11319768203810637304], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 14610958219245486231]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-bf0f8947cda5d3fd\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}