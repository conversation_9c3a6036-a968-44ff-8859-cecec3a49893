use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub scan_paths: Vec<PathBuf>,
    pub volume: f32,
    pub auto_scan_on_startup: bool,
    pub library_cache_path: PathBuf,
    pub keybindings: KeyBindings,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct KeyBindings {
    pub quit: String,
    pub play_pause: String,
    pub stop: String,
    pub next_track: String,
    pub previous_track: String,
    pub volume_up: String,
    pub volume_down: String,
    pub navigate_up: String,
    pub navigate_down: String,
    pub navigate_left: String,
    pub navigate_right: String,
    pub select: String,
    pub toggle_view: String,
    pub scan: String,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            scan_paths: Vec::new(),
            volume: 0.7,
            auto_scan_on_startup: true,
            library_cache_path: PathBuf::from("music_library.json"),
            keybindings: KeyBindings::default(),
        }
    }
}

impl Default for KeyBindings {
    fn default() -> Self {
        Self {
            quit: "q".to_string(),
            play_pause: "Space".to_string(),
            stop: "s".to_string(),
            next_track: "n".to_string(),
            previous_track: "b".to_string(),
            volume_up: "+".to_string(),
            volume_down: "-".to_string(),
            navigate_up: "k".to_string(),
            navigate_down: "j".to_string(),
            navigate_left: "h".to_string(),
            navigate_right: "l".to_string(),
            select: "Enter".to_string(),
            toggle_view: "Tab".to_string(),
            scan: "F5".to_string(),
        }
    }
}

impl Config {
    pub fn load() -> Result<Self> {
        // Try to load from config file, fallback to default
        Ok(Self::default())
    }
    
    pub fn save(&self) -> Result<()> {
        // Save config to file
        Ok(())
    }
}
