use anyhow::Result;
use echo_player::player::MusicPlayer;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize the music player
    let mut player = MusicPlayer::new().await?;

    // Start scanning for music files
    println!("Starting music library scan...");
    player.scan_library().await?;

    // Run the player
    println!("Starting Echo Music Player...");
    player.run().await?;

    Ok(())
}
