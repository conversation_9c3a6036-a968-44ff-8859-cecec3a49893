{"rustc": 10895048813736897673, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 15657897354478470176, "path": 16783973399325666972, "deps": [[5230392855116717286, "equivalent", false, 11660102138251379286], [9150530836556604396, "allocator_api2", false, 7939419125456004468], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 12009160825637845241]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-234fb2ae7baa694a\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}