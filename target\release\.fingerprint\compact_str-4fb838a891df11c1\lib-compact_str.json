{"rustc": 10895048813736897673, "features": "[\"default\", \"std\"]", "declared_features": "[\"arbitrary\", \"borsh\", \"bytes\", \"default\", \"diesel\", \"markup\", \"proptest\", \"quickcheck\", \"rkyv\", \"serde\", \"smallvec\", \"sqlx\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"std\"]", "target": 7968499388442294171, "profile": 2040997289075261528, "path": 2986500799718293202, "deps": [[1216309103264968120, "ryu", false, 615113317846672561], [2828590642173593838, "cfg_if", false, 12452064124578354707], [7695812897323945497, "itoa", false, 10744281287784557910], [7858942147296547339, "rustversion", false, 1877130423347679872], [13785866025199020095, "static_assertions", false, 10434391037107983950], [13914037565384808939, "castaway", false, 2411526134933471263]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\compact_str-4fb838a891df11c1\\dep-lib-compact_str", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}