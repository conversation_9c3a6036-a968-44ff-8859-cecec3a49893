{"rustc": 10895048813736897673, "features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"events\", \"windows\"]", "declared_features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"event-stream\", \"events\", \"filedescriptor\", \"libc\", \"osc52\", \"serde\", \"use-dev-tty\", \"windows\"]", "target": 7162149947039624270, "profile": 2040997289075261528, "path": 427690204101721828, "deps": [[4495526598637097934, "parking_lot", false, 3188055018814405558], [7896293946984509699, "bitflags", false, 6025782646114715211], [10020888071089587331, "<PERSON>ap<PERSON>", false, 15770826305177834564], [11293676373856528358, "derive_more", false, 15306731641786825429], [11763018104473073732, "document_features", false, 17209074276758519192], [17658759660230624279, "crossterm_winapi", false, 11701792900949841278]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\crossterm-e706457a4cf207b6\\dep-lib-crossterm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}