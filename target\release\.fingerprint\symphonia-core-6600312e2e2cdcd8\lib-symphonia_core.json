{"rustc": 10895048813736897673, "features": "[\"default\"]", "declared_features": "[\"default\", \"opt-simd\", \"opt-simd-avx\", \"opt-simd-neon\", \"opt-simd-sse\", \"rustfft\"]", "target": 18060829941030025544, "profile": 2040997289075261528, "path": 5309126760894215592, "deps": [[5986029879202738730, "log", false, 17676981396446242802], [6511429716036861196, "bytemuck", false, 3320693110631568072], [10435729446543529114, "bitflags", false, 14324967619597382688], [13847662864258534762, "arrayvec", false, 6720747025971494555], [17917672826516349275, "lazy_static", false, 470155026414444443]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\symphonia-core-6600312e2e2cdcd8\\dep-lib-symphonia_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}