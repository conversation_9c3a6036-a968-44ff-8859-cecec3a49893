{"rustc": 10895048813736897673, "features": "[\"default\", \"is_variant\", \"std\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 7165309211519594838, "profile": 9707243360411733951, "path": 15507524079731611770, "deps": [[15774985133158646067, "derive_more_impl", false, 3903346307068155790]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\derive_more-9e60ba5f957ce586\\dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}