{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 2684992013079232362, "profile": 2040997289075261528, "path": 18181702015257684710, "deps": [[1218881066841546592, "symphonia_core", false, 15007843837591568806], [5986029879202738730, "log", false, 17676981396446242802], [14564311161534545801, "encoding_rs", false, 3764227264417489071], [17917672826516349275, "lazy_static", false, 470155026414444443]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\symphonia-metadata-48501c9b4a4045c1\\dep-lib-symphonia_metadata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}