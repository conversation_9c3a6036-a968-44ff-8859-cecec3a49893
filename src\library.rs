use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

use crate::scanner::AudioFile;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MusicLibrary {
    pub files: Vec<AudioFile>,
    pub artists: HashMap<String, Vec<usize>>,
    pub albums: HashMap<String, Vec<usize>>,
    pub genres: HashMap<String, Vec<usize>>,
}

impl MusicLibrary {
    pub fn new() -> Self {
        Self {
            files: Vec::new(),
            artists: HashMap::new(),
            albums: HashMap::new(),
            genres: HashMap::new(),
        }
    }
    
    pub fn add_files(&mut self, files: Vec<AudioFile>) {
        for file in files {
            let index = self.files.len();
            
            // Index by artist
            if let Some(artist) = &file.artist {
                self.artists.entry(artist.clone()).or_insert_with(Vec::new).push(index);
            }
            
            // Index by album
            if let Some(album) = &file.album {
                self.albums.entry(album.clone()).or_insert_with(Vec::new).push(index);
            }
            
            // Index by genre
            if let Some(genre) = &file.genre {
                self.genres.entry(genre.clone()).or_insert_with(Vec::new).push(index);
            }
            
            self.files.push(file);
        }
    }
    
    pub fn get_files_by_artist(&self, artist: &str) -> Vec<&AudioFile> {
        if let Some(indices) = self.artists.get(artist) {
            indices.iter().filter_map(|&i| self.files.get(i)).collect()
        } else {
            Vec::new()
        }
    }
    
    pub fn get_files_by_album(&self, album: &str) -> Vec<&AudioFile> {
        if let Some(indices) = self.albums.get(album) {
            indices.iter().filter_map(|&i| self.files.get(i)).collect()
        } else {
            Vec::new()
        }
    }
    
    pub fn get_files_by_genre(&self, genre: &str) -> Vec<&AudioFile> {
        if let Some(indices) = self.genres.get(genre) {
            indices.iter().filter_map(|&i| self.files.get(i)).collect()
        } else {
            Vec::new()
        }
    }
    
    pub fn search(&self, query: &str) -> Vec<&AudioFile> {
        let query_lower = query.to_lowercase();
        self.files.iter().filter(|file| {
            file.title.as_ref().map_or(false, |t| t.to_lowercase().contains(&query_lower)) ||
            file.artist.as_ref().map_or(false, |a| a.to_lowercase().contains(&query_lower)) ||
            file.album.as_ref().map_or(false, |a| a.to_lowercase().contains(&query_lower))
        }).collect()
    }
    
    pub fn get_all_artists(&self) -> Vec<&String> {
        self.artists.keys().collect()
    }
    
    pub fn get_all_albums(&self) -> Vec<&String> {
        self.albums.keys().collect()
    }
    
    pub fn get_all_genres(&self) -> Vec<&String> {
        self.genres.keys().collect()
    }
    
    pub fn len(&self) -> usize {
        self.files.len()
    }
    
    pub fn is_empty(&self) -> bool {
        self.files.is_empty()
    }
}
