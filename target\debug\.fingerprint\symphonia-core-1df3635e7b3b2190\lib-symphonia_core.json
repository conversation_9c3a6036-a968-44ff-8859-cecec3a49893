{"rustc": 10895048813736897673, "features": "[\"default\"]", "declared_features": "[\"default\", \"opt-simd\", \"opt-simd-avx\", \"opt-simd-neon\", \"opt-simd-sse\", \"rustfft\"]", "target": 18060829941030025544, "profile": 15657897354478470176, "path": 5309126760894215592, "deps": [[5986029879202738730, "log", false, 11226432392582311476], [6511429716036861196, "bytemuck", false, 7001415989473815484], [10435729446543529114, "bitflags", false, 3547454844564863130], [13847662864258534762, "arrayvec", false, 5889172888726832144], [17917672826516349275, "lazy_static", false, 4862664117436432470]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\symphonia-core-1df3635e7b3b2190\\dep-lib-symphonia_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}