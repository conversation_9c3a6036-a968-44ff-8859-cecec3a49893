{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 2684992013079232362, "profile": 2241668132362809309, "path": 18181702015257684710, "deps": [[1218881066841546592, "symphonia_core", false, 7951852861347307535], [5986029879202738730, "log", false, 8328376911722723733], [14564311161534545801, "encoding_rs", false, 16117708874835113723], [17917672826516349275, "lazy_static", false, 1693215947347874771]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\symphonia-metadata-6b866b9cc08991dd\\dep-lib-symphonia_metadata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}