use echo_player::scanner::AudioScanner;
use echo_player::library::MusicLibrary;
use std::path::PathBuf;

#[tokio::test]
async fn test_audio_scanner_creation() {
    let scanner = AudioScanner::new();
    
    // Test that scanner recognizes audio files
    assert!(scanner.is_audio_file(&PathBuf::from("test.mp3")));
    assert!(scanner.is_audio_file(&PathBuf::from("test.flac")));
    assert!(scanner.is_audio_file(&PathBuf::from("test.wav")));
    assert!(!scanner.is_audio_file(&PathBuf::from("test.txt")));
}

#[test]
fn test_music_library() {
    let mut library = MusicLibrary::new();
    assert!(library.is_empty());
    assert_eq!(library.len(), 0);
    
    // Test that library can be created and is initially empty
    let artists = library.get_all_artists();
    assert!(artists.is_empty());
}

#[test]
fn test_library_search() {
    let library = MusicLibrary::new();
    let results = library.search("test");
    assert!(results.is_empty());
}
