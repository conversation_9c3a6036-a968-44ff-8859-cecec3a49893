use anyhow::Result;
use crossterm::{
    event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode, KeyEvent, KeyModifiers},
    execute,
    terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},
};
use ratatui::{
    backend::CrosstermBackend,
    layout::{Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph},
    Frame, Terminal,
};
use std::io;
use std::time::{Duration, Instant};
use tokio::sync::mpsc;

use crate::scanner::AudioFile;

#[derive(Debug, Clone)]
pub enum UIEvent {
    Quit,
    Play,
    Pause,
    Stop,
    NextTrack,
    PreviousTrack,
    VolumeUp,
    VolumeDown,
    ToggleLibrary,
    NavigateUp,
    NavigateDown,
    NavigateLeft,
    NavigateRight,
    Select,
    <PERSON><PERSON>,
}

#[derive(Debug, Clone)]
pub enum UIState {
    Library,
    Player,
    Scanning,
}

pub struct UI {
    terminal: Terminal<CrosstermBackend<io::Stdout>>,
    event_tx: mpsc::UnboundedSender<UIEvent>,
    event_rx: mpsc::UnboundedReceiver<UIEvent>,
    state: UIState,
    library_state: ListState,
    audio_files: Vec<AudioFile>,
    current_track: Option<usize>,
    volume: f32,
    is_playing: bool,
    scan_progress: Option<(usize, usize)>,
}

impl UI {
    pub fn new() -> Result<Self> {
        enable_raw_mode()?;
        let mut stdout = io::stdout();
        execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
        let backend = CrosstermBackend::new(stdout);
        let terminal = Terminal::new(backend)?;
        
        let (event_tx, event_rx) = mpsc::unbounded_channel();
        
        Ok(Self {
            terminal,
            event_tx,
            event_rx,
            state: UIState::Library,
            library_state: ListState::default(),
            audio_files: Vec::new(),
            current_track: None,
            volume: 0.7,
            is_playing: false,
            scan_progress: None,
        })
    }
    
    pub async fn run(&mut self) -> Result<()> {
        let event_tx = self.event_tx.clone();
        
        // Spawn keyboard event handler
        tokio::spawn(async move {
            loop {
                // 120Hz refresh rate = ~8.33ms per frame
                if event::poll(Duration::from_millis(8)).unwrap_or(false) {
                    if let Ok(event) = event::read() {
                        if let Event::Key(key) = event {
                            if let Some(ui_event) = Self::map_key_event(key) {
                                if event_tx.send(ui_event).is_err() {
                                    break;
                                }
                            }
                        }
                    }
                }
                tokio::time::sleep(Duration::from_millis(8)).await;
            }
        });
        
        let mut last_frame = Instant::now();
        let frame_duration = Duration::from_millis(8); // 120Hz
        
        loop {
            // Handle UI events
            while let Ok(event) = self.event_rx.try_recv() {
                match event {
                    UIEvent::Quit => return Ok(()),
                    UIEvent::NavigateUp => self.navigate_up(),
                    UIEvent::NavigateDown => self.navigate_down(),
                    UIEvent::Select => self.handle_select(),
                    UIEvent::ToggleLibrary => self.toggle_state(),
                    _ => {} // Handle other events as needed
                }
            }
            
            // Maintain 120Hz refresh rate
            let now = Instant::now();
            if now.duration_since(last_frame) >= frame_duration {
                self.draw()?;
                last_frame = now;
            }
            
            tokio::time::sleep(Duration::from_millis(1)).await;
        }
    }
    
    fn map_key_event(key: KeyEvent) -> Option<UIEvent> {
        match key.code {
            KeyCode::Char('q') | KeyCode::Esc => Some(UIEvent::Quit),
            KeyCode::Char(' ') => Some(UIEvent::Play),
            KeyCode::Char('p') => Some(UIEvent::Pause),
            KeyCode::Char('s') => Some(UIEvent::Stop),
            KeyCode::Up | KeyCode::Char('k') => Some(UIEvent::NavigateUp),
            KeyCode::Down | KeyCode::Char('j') => Some(UIEvent::NavigateDown),
            KeyCode::Left | KeyCode::Char('h') => Some(UIEvent::NavigateLeft),
            KeyCode::Right | KeyCode::Char('l') => Some(UIEvent::NavigateRight),
            KeyCode::Enter => Some(UIEvent::Select),
            KeyCode::Tab => Some(UIEvent::ToggleLibrary),
            KeyCode::Char('n') => Some(UIEvent::NextTrack),
            KeyCode::Char('b') => Some(UIEvent::PreviousTrack),
            KeyCode::Char('+') | KeyCode::Char('=') => Some(UIEvent::VolumeUp),
            KeyCode::Char('-') => Some(UIEvent::VolumeDown),
            KeyCode::F(5) => Some(UIEvent::Scan),
            _ => None,
        }
    }
    
    fn draw(&mut self) -> Result<()> {
        let state = self.state.clone();
        let audio_files = self.audio_files.clone();
        let current_track = self.current_track;
        let volume = self.volume;
        let is_playing = self.is_playing;
        let scan_progress = self.scan_progress;

        self.terminal.draw(|f| {
            let chunks = Layout::default()
                .direction(Direction::Vertical)
                .constraints([
                    Constraint::Min(3),     // Main content
                    Constraint::Length(3),  // Player controls
                    Constraint::Length(1),  // Status bar
                ])
                .split(f.area());

            match state {
                UIState::Library => Self::draw_library_static(f, chunks[0], &audio_files, current_track, &mut self.library_state),
                UIState::Player => Self::draw_player_static(f, chunks[0], &audio_files, current_track, is_playing),
                UIState::Scanning => Self::draw_scanning_static(f, chunks[0], scan_progress),
            }

            Self::draw_controls_static(f, chunks[1], volume);
            Self::draw_status_static(f, chunks[2], audio_files.len(), &state);
        })?;

        Ok(())
    }
    
    fn draw_library_static(f: &mut Frame, area: Rect, audio_files: &[AudioFile], current_track: Option<usize>, library_state: &mut ListState) {
        let items: Vec<ListItem> = audio_files
            .iter()
            .enumerate()
            .map(|(i, file)| {
                let style = if Some(i) == current_track {
                    Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                } else {
                    Style::default()
                };

                let title = file.title.as_deref().unwrap_or("Unknown");
                let artist = file.artist.as_deref().unwrap_or("Unknown Artist");

                ListItem::new(Line::from(vec![
                    Span::styled(format!("{} - {}", artist, title), style)
                ]))
            })
            .collect();

        let list = List::new(items)
            .block(Block::default().borders(Borders::ALL).title("Music Library"))
            .highlight_style(Style::default().bg(Color::DarkGray))
            .highlight_symbol("► ");

        f.render_stateful_widget(list, area, library_state);
    }
    
    fn draw_player_static(f: &mut Frame, area: Rect, audio_files: &[AudioFile], current_track: Option<usize>, is_playing: bool) {
        let block = Block::default().borders(Borders::ALL).title("Now Playing");

        if let Some(track_idx) = current_track {
            if let Some(track) = audio_files.get(track_idx) {
                let title = track.title.as_deref().unwrap_or("Unknown");
                let artist = track.artist.as_deref().unwrap_or("Unknown Artist");
                let album = track.album.as_deref().unwrap_or("Unknown Album");

                let text = vec![
                    Line::from(vec![Span::styled("Title: ", Style::default().add_modifier(Modifier::BOLD)), Span::raw(title)]),
                    Line::from(vec![Span::styled("Artist: ", Style::default().add_modifier(Modifier::BOLD)), Span::raw(artist)]),
                    Line::from(vec![Span::styled("Album: ", Style::default().add_modifier(Modifier::BOLD)), Span::raw(album)]),
                    Line::from(""),
                    Line::from(if is_playing { "▶ Playing" } else { "⏸ Paused" }),
                ];

                let paragraph = Paragraph::new(text).block(block);
                f.render_widget(paragraph, area);
            }
        } else {
            let paragraph = Paragraph::new("No track selected").block(block);
            f.render_widget(paragraph, area);
        }
    }
    
    fn draw_scanning_static(f: &mut Frame, area: Rect, scan_progress: Option<(usize, usize)>) {
        let block = Block::default().borders(Borders::ALL).title("Scanning for Music Files");

        if let Some((current, total)) = scan_progress {
            let progress = if total > 0 { current as f64 / total as f64 } else { 0.0 };
            let gauge = Gauge::default()
                .block(block)
                .gauge_style(Style::default().fg(Color::Cyan))
                .percent((progress * 100.0) as u16)
                .label(format!("Scanned {} files", current));

            f.render_widget(gauge, area);
        } else {
            let paragraph = Paragraph::new("Initializing scan...").block(block);
            f.render_widget(paragraph, area);
        }
    }
    
    fn draw_controls_static(f: &mut Frame, area: Rect, volume: f32) {
        let volume_gauge = Gauge::default()
            .block(Block::default().borders(Borders::ALL).title("Volume"))
            .gauge_style(Style::default().fg(Color::Green))
            .percent((volume * 100.0) as u16);

        f.render_widget(volume_gauge, area);
    }

    fn draw_status_static(f: &mut Frame, area: Rect, file_count: usize, state: &UIState) {
        let status_text = format!(
            "Files: {} | State: {:?} | Controls: Space=Play/Pause, Q=Quit, Tab=Switch View, F5=Scan",
            file_count,
            state
        );

        let paragraph = Paragraph::new(status_text)
            .style(Style::default().bg(Color::DarkGray));

        f.render_widget(paragraph, area);
    }
    
    fn navigate_up(&mut self) {
        if let Some(selected) = self.library_state.selected() {
            if selected > 0 {
                self.library_state.select(Some(selected - 1));
            }
        } else if !self.audio_files.is_empty() {
            self.library_state.select(Some(0));
        }
    }
    
    fn navigate_down(&mut self) {
        if let Some(selected) = self.library_state.selected() {
            if selected < self.audio_files.len().saturating_sub(1) {
                self.library_state.select(Some(selected + 1));
            }
        } else if !self.audio_files.is_empty() {
            self.library_state.select(Some(0));
        }
    }
    
    fn handle_select(&mut self) {
        if let Some(selected) = self.library_state.selected() {
            self.current_track = Some(selected);
            self.state = UIState::Player;
        }
    }
    
    fn toggle_state(&mut self) {
        self.state = match self.state {
            UIState::Library => UIState::Player,
            UIState::Player => UIState::Library,
            UIState::Scanning => UIState::Library,
        };
    }
    
    pub fn set_audio_files(&mut self, files: Vec<AudioFile>) {
        self.audio_files = files;
        if !self.audio_files.is_empty() && self.library_state.selected().is_none() {
            self.library_state.select(Some(0));
        }
    }
    
    pub fn set_scan_progress(&mut self, current: usize, total: usize) {
        self.scan_progress = Some((current, total));
    }
    
    pub fn set_scanning_state(&mut self, scanning: bool) {
        if scanning {
            self.state = UIState::Scanning;
        } else {
            self.state = UIState::Library;
        }
    }
    
    pub fn get_event_sender(&self) -> mpsc::UnboundedSender<UIEvent> {
        self.event_tx.clone()
    }
}

impl Drop for UI {
    fn drop(&mut self) {
        let _ = disable_raw_mode();
        let _ = execute!(
            self.terminal.backend_mut(),
            LeaveAlternateScreen,
            DisableMouseCapture
        );
    }
}
