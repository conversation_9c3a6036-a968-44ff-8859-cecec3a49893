use anyhow::Result;
use crossterm::{
    event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode, KeyEvent, KeyModifiers},
    execute,
    terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},
};
use ratatui::{
    backend::CrosstermBackend,
    layout::{Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph},
    Frame, Terminal,
};

// Color palette based on the provided image
const DARK_BLUE: Color = Color::Rgb(31, 85, 130);      // #1f5582
const LIGHT_BLUE: Color = Color::Rgb(168, 208, 240);   // #a8d0f0
const LIGHT_GRAY: Color = Color::Rgb(245, 245, 245);   // #f5f5f5
const CORAL: Color = Color::Rgb(255, 107, 71);         // #ff6b47
use std::io;
use std::time::{Duration, Instant};
use tokio::sync::mpsc;

use crate::scanner::AudioFile;

#[derive(Debug, Clone)]
pub enum UIEvent {
    Quit,
    Play,
    Pause,
    Stop,
    NextTrack,
    PreviousTrack,
    VolumeUp,
    VolumeDown,
    ToggleLibrary,
    NavigateUp,
    NavigateDown,
    NavigateLeft,
    NavigateRight,
    Select,
    StartSearch,
    SearchInput(char),
    SearchBackspace,
    ExitSearch,
}

#[derive(Debug, Clone)]
pub enum UIState {
    Library,
    Player,
    Scanning,
    Searching,
}

pub struct UI {
    terminal: Terminal<CrosstermBackend<io::Stdout>>,
    event_tx: mpsc::UnboundedSender<UIEvent>,
    event_rx: mpsc::UnboundedReceiver<UIEvent>,
    state: UIState,
    library_state: ListState,
    audio_files: Vec<AudioFile>,
    current_track: Option<usize>,
    volume: f32,
    is_playing: bool,
    scan_progress: Option<(usize, usize)>,
    library: Option<std::sync::Arc<tokio::sync::Mutex<crate::library::MusicLibrary>>>,
    // Search functionality
    search_query: String,
    search_results: Vec<(usize, f32)>, // (index, relevance_score)
    search_state: ListState,
}

impl UI {
    pub fn new() -> Result<Self> {
        enable_raw_mode()?;
        let mut stdout = io::stdout();
        execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
        let backend = CrosstermBackend::new(stdout);
        let terminal = Terminal::new(backend)?;
        
        let (event_tx, event_rx) = mpsc::unbounded_channel();
        
        Ok(Self {
            terminal,
            event_tx,
            event_rx,
            state: UIState::Library,
            library_state: ListState::default(),
            audio_files: Vec::new(),
            current_track: None,
            volume: 0.7,
            is_playing: false,
            scan_progress: None,
            library: None,
            search_query: String::new(),
            search_results: Vec::new(),
            search_state: ListState::default(),
        })
    }
    
    pub async fn run(&mut self) -> Result<()> {
        let event_tx = self.event_tx.clone();
        
        // Spawn keyboard event handler
        let event_tx_clone = event_tx.clone();
        tokio::spawn(async move {
            loop {
                // 120Hz refresh rate = ~8.33ms per frame
                if event::poll(Duration::from_millis(8)).unwrap_or(false) {
                    if let Ok(event) = event::read() {
                        if let Event::Key(key) = event {
                            // For now, we'll use Library state as default for key mapping
                            // The actual state will be checked in the main loop
                            if let Some(ui_event) = Self::map_key_event(key, &UIState::Library) {
                                if event_tx_clone.send(ui_event).is_err() {
                                    break;
                                }
                            }
                        }
                    }
                }
                tokio::time::sleep(Duration::from_millis(8)).await;
            }
        });
        
        let mut last_frame = Instant::now();
        let frame_duration = Duration::from_millis(8); // 120Hz
        
        loop {
            // Handle UI events
            while let Ok(event) = self.event_rx.try_recv() {
                match event {
                    UIEvent::Quit => return Ok(()),
                    UIEvent::StartSearch => self.start_search(),
                    UIEvent::SearchInput(c) => self.handle_search_input(c),
                    UIEvent::SearchBackspace => self.handle_search_backspace(),
                    UIEvent::ExitSearch => self.exit_search(),
                    UIEvent::NavigateUp => self.navigate_up(),
                    UIEvent::NavigateDown => self.navigate_down(),
                    UIEvent::Select => self.handle_select(),
                    UIEvent::ToggleLibrary => self.toggle_state(),
                    _ => {} // Handle other events as needed
                }
            }

            // Update library contents if available
            if let Some(library_ref) = &self.library {
                if let Ok(library) = library_ref.try_lock() {
                    if library.len() != self.audio_files.len() {
                        self.audio_files = library.files.clone();
                        if !self.audio_files.is_empty() && self.library_state.selected().is_none() {
                            self.library_state.select(Some(0));
                        }
                    }
                }
            }

            // Maintain 120Hz refresh rate
            let now = Instant::now();
            if now.duration_since(last_frame) >= frame_duration {
                self.draw()?;
                last_frame = now;
            }
            
            tokio::time::sleep(Duration::from_millis(1)).await;
        }
    }
    
    fn map_key_event(key: KeyEvent, ui_state: &UIState) -> Option<UIEvent> {
        use crossterm::event::KeyModifiers;

        match ui_state {
            UIState::Searching => {
                match key.code {
                    KeyCode::Esc => Some(UIEvent::ExitSearch),
                    KeyCode::Enter => Some(UIEvent::Select),
                    KeyCode::Backspace => Some(UIEvent::SearchBackspace),
                    KeyCode::Up | KeyCode::Char('k') => Some(UIEvent::NavigateUp),
                    KeyCode::Down | KeyCode::Char('j') => Some(UIEvent::NavigateDown),
                    KeyCode::Char(c) => Some(UIEvent::SearchInput(c)),
                    _ => None,
                }
            }
            _ => {
                match key.code {
                    KeyCode::Char('q') | KeyCode::Esc => Some(UIEvent::Quit),
                    KeyCode::Char('f') if key.modifiers.contains(KeyModifiers::CONTROL) => Some(UIEvent::StartSearch),
                    KeyCode::Char(' ') => Some(UIEvent::Play),
                    KeyCode::Char('p') => Some(UIEvent::Pause),
                    KeyCode::Char('s') => Some(UIEvent::Stop),
                    KeyCode::Up | KeyCode::Char('k') => Some(UIEvent::NavigateUp),
                    KeyCode::Down | KeyCode::Char('j') => Some(UIEvent::NavigateDown),
                    KeyCode::Left | KeyCode::Char('h') => Some(UIEvent::NavigateLeft),
                    KeyCode::Right | KeyCode::Char('l') => Some(UIEvent::NavigateRight),
                    KeyCode::Enter => Some(UIEvent::Select),
                    KeyCode::Tab => Some(UIEvent::ToggleLibrary),
                    KeyCode::Char('n') => Some(UIEvent::NextTrack),
                    KeyCode::Char('b') => Some(UIEvent::PreviousTrack),
                    KeyCode::Char('+') | KeyCode::Char('=') => Some(UIEvent::VolumeUp),
                    KeyCode::Char('-') => Some(UIEvent::VolumeDown),
                    _ => None,
                }
            }
        }
    }
    
    fn draw(&mut self) -> Result<()> {
        let state = self.state.clone();
        let audio_files = self.audio_files.clone();
        let current_track = self.current_track;
        let volume = self.volume;
        let is_playing = self.is_playing;
        let scan_progress = self.scan_progress;

        self.terminal.draw(|f| {
            let chunks = Layout::default()
                .direction(Direction::Vertical)
                .constraints([
                    Constraint::Min(3),     // Main content
                    Constraint::Length(3),  // Player controls
                    Constraint::Length(1),  // Status bar
                ])
                .split(f.area());

            match state {
                UIState::Library => Self::draw_library_static(f, chunks[0], &audio_files, current_track, &mut self.library_state),
                UIState::Player => Self::draw_player_static(f, chunks[0], &audio_files, current_track, is_playing),
                UIState::Scanning => Self::draw_scanning_static(f, chunks[0], scan_progress),
                UIState::Searching => Self::draw_search_static(f, chunks[0], &audio_files, &self.search_query, &self.search_results, &mut self.search_state),
            }

            Self::draw_controls_static(f, chunks[1], volume);
            Self::draw_status_static(f, chunks[2], audio_files.len(), &state);
        })?;

        Ok(())
    }
    
    fn draw_library_static(f: &mut Frame, area: Rect, audio_files: &[AudioFile], current_track: Option<usize>, library_state: &mut ListState) {
        let items: Vec<ListItem> = audio_files
            .iter()
            .enumerate()
            .map(|(i, file)| {
                let style = if Some(i) == current_track {
                    Style::default().fg(CORAL).add_modifier(Modifier::BOLD)
                } else {
                    Style::default().fg(LIGHT_GRAY)
                };

                let title = file.title.as_deref().unwrap_or("Unknown");
                let artist = file.artist.as_deref().unwrap_or("Unknown Artist");

                ListItem::new(Line::from(vec![
                    Span::styled(format!("{} - {}", artist, title), style)
                ]))
            })
            .collect();

        let list = List::new(items)
            .block(Block::default()
                .borders(Borders::ALL)
                .title("Music Library")
                .border_style(Style::default().fg(DARK_BLUE))
                .title_style(Style::default().fg(DARK_BLUE).add_modifier(Modifier::BOLD)))
            .highlight_style(Style::default().bg(LIGHT_BLUE).fg(DARK_BLUE))
            .highlight_symbol("► ");

        f.render_stateful_widget(list, area, library_state);
    }
    
    fn draw_player_static(f: &mut Frame, area: Rect, audio_files: &[AudioFile], current_track: Option<usize>, is_playing: bool) {
        let block = Block::default()
            .borders(Borders::ALL)
            .title("Now Playing")
            .border_style(Style::default().fg(DARK_BLUE))
            .title_style(Style::default().fg(DARK_BLUE).add_modifier(Modifier::BOLD));

        if let Some(track_idx) = current_track {
            if let Some(track) = audio_files.get(track_idx) {
                let title = track.title.as_deref().unwrap_or("Unknown");
                let artist = track.artist.as_deref().unwrap_or("Unknown Artist");
                let album = track.album.as_deref().unwrap_or("Unknown Album");

                let text = vec![
                    Line::from(vec![
                        Span::styled("Title: ", Style::default().fg(DARK_BLUE).add_modifier(Modifier::BOLD)),
                        Span::styled(title, Style::default().fg(LIGHT_GRAY))
                    ]),
                    Line::from(vec![
                        Span::styled("Artist: ", Style::default().fg(DARK_BLUE).add_modifier(Modifier::BOLD)),
                        Span::styled(artist, Style::default().fg(LIGHT_GRAY))
                    ]),
                    Line::from(vec![
                        Span::styled("Album: ", Style::default().fg(DARK_BLUE).add_modifier(Modifier::BOLD)),
                        Span::styled(album, Style::default().fg(LIGHT_GRAY))
                    ]),
                    Line::from(""),
                    Line::from(vec![
                        Span::styled(
                            if is_playing { "▶ Playing" } else { "⏸ Paused" },
                            Style::default().fg(CORAL).add_modifier(Modifier::BOLD)
                        )
                    ]),
                ];

                let paragraph = Paragraph::new(text).block(block);
                f.render_widget(paragraph, area);
            }
        } else {
            let paragraph = Paragraph::new(
                vec![Line::from(vec![
                    Span::styled("No track selected", Style::default().fg(LIGHT_GRAY))
                ])]
            ).block(block);
            f.render_widget(paragraph, area);
        }
    }
    
    fn draw_scanning_static(f: &mut Frame, area: Rect, scan_progress: Option<(usize, usize)>) {
        let block = Block::default()
            .borders(Borders::ALL)
            .title("Scanning for Music Files")
            .border_style(Style::default().fg(DARK_BLUE))
            .title_style(Style::default().fg(DARK_BLUE).add_modifier(Modifier::BOLD));

        if let Some((current, total)) = scan_progress {
            let progress = if total > 0 { current as f64 / total as f64 } else { 0.0 };
            let gauge = Gauge::default()
                .block(block)
                .gauge_style(Style::default().fg(LIGHT_BLUE))
                .percent((progress * 100.0) as u16)
                .label(format!("Scanned {} files", current));

            f.render_widget(gauge, area);
        } else {
            let paragraph = Paragraph::new(
                vec![Line::from(vec![
                    Span::styled("Initializing scan...", Style::default().fg(LIGHT_GRAY))
                ])]
            ).block(block);
            f.render_widget(paragraph, area);
        }
    }
    
    fn draw_controls_static(f: &mut Frame, area: Rect, volume: f32) {
        let volume_gauge = Gauge::default()
            .block(Block::default()
                .borders(Borders::ALL)
                .title("Volume")
                .border_style(Style::default().fg(DARK_BLUE))
                .title_style(Style::default().fg(DARK_BLUE).add_modifier(Modifier::BOLD)))
            .gauge_style(Style::default().fg(CORAL))
            .percent((volume * 100.0) as u16);

        f.render_widget(volume_gauge, area);
    }

    fn draw_search_static(f: &mut Frame, area: Rect, audio_files: &[AudioFile], search_query: &str, search_results: &[(usize, f32)], search_state: &mut ListState) {
        // Split area for search input and results
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(3), // Search input
                Constraint::Min(0),    // Search results
            ])
            .split(area);

        // Draw search input box
        let search_input = Paragraph::new(format!("Search: {}", search_query))
            .block(Block::default()
                .borders(Borders::ALL)
                .title("Search Music (Ctrl+F)")
                .border_style(Style::default().fg(CORAL))
                .title_style(Style::default().fg(CORAL).add_modifier(Modifier::BOLD)))
            .style(Style::default().fg(LIGHT_GRAY));

        f.render_widget(search_input, chunks[0]);

        // Draw search results
        let items: Vec<ListItem> = search_results
            .iter()
            .map(|(index, score)| {
                if let Some(file) = audio_files.get(*index) {
                    let title = file.title.as_deref().unwrap_or("Unknown");
                    let artist = file.artist.as_deref().unwrap_or("Unknown Artist");

                    // Show relevance with different colors
                    let style = if *score >= 3.0 {
                        Style::default().fg(CORAL).add_modifier(Modifier::BOLD)
                    } else if *score >= 2.0 {
                        Style::default().fg(LIGHT_BLUE)
                    } else {
                        Style::default().fg(LIGHT_GRAY)
                    };

                    ListItem::new(Line::from(vec![
                        Span::styled(format!("{} - {} ({:.1})", artist, title, score), style)
                    ]))
                } else {
                    ListItem::new(Line::from("Invalid result"))
                }
            })
            .collect();

        let results_list = List::new(items)
            .block(Block::default()
                .borders(Borders::ALL)
                .title(format!("Results ({}) - ESC to exit", search_results.len()))
                .border_style(Style::default().fg(DARK_BLUE))
                .title_style(Style::default().fg(DARK_BLUE).add_modifier(Modifier::BOLD)))
            .highlight_style(Style::default().bg(LIGHT_BLUE).fg(DARK_BLUE))
            .highlight_symbol("► ");

        f.render_stateful_widget(results_list, chunks[1], search_state);
    }

    fn draw_status_static(f: &mut Frame, area: Rect, file_count: usize, state: &UIState) {
        let status_text = match state {
            UIState::Searching => "Search Mode - Type to search, ESC to exit, Enter to select".to_string(),
            _ => format!(
                "Files: {} | State: {:?} | Controls: Space=Play/Pause, Q=Quit, Tab=Switch View, Ctrl+F=Search",
                file_count,
                state
            ),
        };

        let paragraph = Paragraph::new(status_text)
            .style(Style::default().bg(DARK_BLUE).fg(LIGHT_GRAY));

        f.render_widget(paragraph, area);
    }
    
    fn navigate_up(&mut self) {
        if let Some(selected) = self.library_state.selected() {
            if selected > 0 {
                self.library_state.select(Some(selected - 1));
            }
        } else if !self.audio_files.is_empty() {
            self.library_state.select(Some(0));
        }
    }
    
    fn navigate_down(&mut self) {
        if let Some(selected) = self.library_state.selected() {
            if selected < self.audio_files.len().saturating_sub(1) {
                self.library_state.select(Some(selected + 1));
            }
        } else if !self.audio_files.is_empty() {
            self.library_state.select(Some(0));
        }
    }
    
    fn handle_select(&mut self) {
        if let Some(selected) = self.library_state.selected() {
            self.current_track = Some(selected);
            self.state = UIState::Player;
        }
    }
    
    fn toggle_state(&mut self) {
        self.state = match self.state {
            UIState::Library => UIState::Player,
            UIState::Player => UIState::Library,
            UIState::Scanning => UIState::Library,
            UIState::Searching => UIState::Library,
        };
    }
    
    pub fn set_audio_files(&mut self, files: Vec<AudioFile>) {
        self.audio_files = files;
        if !self.audio_files.is_empty() && self.library_state.selected().is_none() {
            self.library_state.select(Some(0));
        }
    }
    
    pub fn set_scan_progress(&mut self, current: usize, total: usize) {
        self.scan_progress = Some((current, total));
    }
    
    pub fn set_scanning_state(&mut self, scanning: bool) {
        if scanning {
            self.state = UIState::Scanning;
        } else {
            self.state = UIState::Library;
        }
    }
    
    pub fn get_event_sender(&self) -> mpsc::UnboundedSender<UIEvent> {
        self.event_tx.clone()
    }

    pub fn set_library_reference(&mut self, library: std::sync::Arc<tokio::sync::Mutex<crate::library::MusicLibrary>>) {
        self.library = Some(library);
    }

    // Search functionality methods
    fn start_search(&mut self) {
        self.state = UIState::Searching;
        self.search_query.clear();
        self.search_results.clear();
        self.search_state.select(None);
    }

    fn handle_search_input(&mut self, c: char) {
        self.search_query.push(c);
        self.update_search_results();
    }

    fn handle_search_backspace(&mut self) {
        self.search_query.pop();
        self.update_search_results();
    }

    fn exit_search(&mut self) {
        self.state = UIState::Library;
        self.search_query.clear();
        self.search_results.clear();
    }

    fn update_search_results(&mut self) {
        self.search_results.clear();

        if self.search_query.is_empty() {
            return;
        }

        let query_lower = self.search_query.to_lowercase();

        for (index, file) in self.audio_files.iter().enumerate() {
            let mut score = 0.0f32;

            // Check title match
            if let Some(title) = &file.title {
                let title_lower = title.to_lowercase();
                if title_lower.contains(&query_lower) {
                    score += if title_lower.starts_with(&query_lower) { 3.0 } else { 2.0 };
                }
            }

            // Check artist match
            if let Some(artist) = &file.artist {
                let artist_lower = artist.to_lowercase();
                if artist_lower.contains(&query_lower) {
                    score += if artist_lower.starts_with(&query_lower) { 2.5 } else { 1.5 };
                }
            }

            // Check album match
            if let Some(album) = &file.album {
                let album_lower = album.to_lowercase();
                if album_lower.contains(&query_lower) {
                    score += if album_lower.starts_with(&query_lower) { 2.0 } else { 1.0 };
                }
            }

            // Check filename match
            let filename = file.path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("")
                .to_lowercase();
            if filename.contains(&query_lower) {
                score += if filename.starts_with(&query_lower) { 1.5 } else { 0.5 };
            }

            if score > 0.0 {
                self.search_results.push((index, score));
            }
        }

        // Sort by relevance score (highest first)
        self.search_results.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        // Select first result if any
        if !self.search_results.is_empty() {
            self.search_state.select(Some(0));
        }
    }
}

impl Drop for UI {
    fn drop(&mut self) {
        let _ = disable_raw_mode();
        let _ = execute!(
            self.terminal.backend_mut(),
            LeaveAlternateScreen,
            DisableMouseCapture
        );
    }
}
