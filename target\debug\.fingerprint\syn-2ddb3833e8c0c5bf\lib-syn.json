{"rustc": 10895048813736897673, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 6718851819208812100, "deps": [[1988483478007900009, "unicode_ident", false, 7655378141152734294], [3060637413840920116, "proc_macro2", false, 18092773636963976281], [17990358020177143287, "quote", false, 5858964599889670858]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-2ddb3833e8c0c5bf\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}