{"rustc": 10895048813736897673, "features": "[\"default\", \"is_variant\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 3601279031184244129, "path": 16654985960813366910, "deps": [[3060637413840920116, "proc_macro2", false, 5237011736313039564], [4974441333307933176, "syn", false, 5624359604238591930], [17685210698997651194, "convert_case", false, 3878505044511288496], [17990358020177143287, "quote", false, 11798361614793102406]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\derive_more-impl-678236b9c1a3f65c\\dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}