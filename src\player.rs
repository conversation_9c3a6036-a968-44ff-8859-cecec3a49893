use anyhow::Result;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};

use crate::audio::{AudioEngine, AudioCommand, AudioEvent};
use crate::library::MusicLibrary;
use crate::scanner::{AudioScanner, ScanEvent};
use crate::ui::{UIEvent, UI};

pub struct MusicPlayer {
    audio_engine: AudioEngine,
    library: Arc<Mutex<MusicLibrary>>,
    scanner: AudioScanner,
    ui: UI,
    current_track_index: Option<usize>,
}

impl MusicPlayer {
    pub async fn new() -> Result<Self> {
        let audio_engine = AudioEngine::new()?;
        let library = Arc::new(Mutex::new(MusicLibrary::new()));
        let scanner = AudioScanner::new();
        let ui = UI::new()?;
        
        Ok(Self {
            audio_engine,
            library,
            scanner,
            ui,
            current_track_index: None,
        })
    }
    
    pub async fn run(&mut self) -> Result<()> {
        // Run the UI
        self.ui.run().await?;

        Ok(())
    }
    
    pub async fn scan_library(&mut self) -> Result<()> {
        let (scan_tx, mut scan_rx) = mpsc::unbounded_channel();
        
        self.ui.set_scanning_state(true);
        
        // Start scanning in background
        let scanner = self.scanner.clone();
        let scan_task = tokio::spawn(async move {
            scanner.scan_system(scan_tx).await
        });
        
        // Handle scan events
        while let Some(event) = scan_rx.recv().await {
            match event {
                ScanEvent::Started => {
                    // Update UI to show scanning started
                }
                ScanEvent::Progress { current, total } => {
                    self.ui.set_scan_progress(current, total);
                }
                ScanEvent::FileFound(file) => {
                    // Add file to library
                    let mut library = self.library.lock().await;
                    library.add_files(vec![file]);
                }
                ScanEvent::Completed { total_files: _ } => {
                    self.ui.set_scanning_state(false);
                    
                    // Update UI with all files
                    let library = self.library.lock().await;
                    self.ui.set_audio_files(library.files.clone());
                    break;
                }
                ScanEvent::Error(err) => {
                    eprintln!("Scan error: {}", err);
                }
            }
        }
        
        // Wait for scan to complete
        if let Ok(files) = scan_task.await? {
            let mut library = self.library.lock().await;
            library.add_files(files);
        }
        
        Ok(())
    }
    
    pub async fn play_track(&mut self, index: usize) -> Result<()> {
        let library = self.library.lock().await;
        if let Some(file) = library.files.get(index) {
            let path = file.path.to_string_lossy().to_string();
            self.audio_engine.send_command(AudioCommand::Play(path))?;
            self.current_track_index = Some(index);
        }
        Ok(())
    }
    
    pub async fn pause(&mut self) -> Result<()> {
        self.audio_engine.send_command(AudioCommand::Pause)?;
        Ok(())
    }
    
    pub async fn resume(&mut self) -> Result<()> {
        self.audio_engine.send_command(AudioCommand::Resume)?;
        Ok(())
    }
    
    pub async fn stop(&mut self) -> Result<()> {
        self.audio_engine.send_command(AudioCommand::Stop)?;
        self.current_track_index = None;
        Ok(())
    }
    
    pub async fn next_track(&mut self) -> Result<()> {
        if let Some(current) = self.current_track_index {
            let library = self.library.lock().await;
            let next_index = (current + 1) % library.files.len();
            drop(library);
            self.play_track(next_index).await?;
        }
        Ok(())
    }
    
    pub async fn previous_track(&mut self) -> Result<()> {
        if let Some(current) = self.current_track_index {
            let library = self.library.lock().await;
            let prev_index = if current == 0 {
                library.files.len().saturating_sub(1)
            } else {
                current - 1
            };
            drop(library);
            self.play_track(prev_index).await?;
        }
        Ok(())
    }
    
    pub async fn set_volume(&mut self, volume: f32) -> Result<()> {
        let clamped_volume = volume.clamp(0.0, 1.0);
        self.audio_engine.send_command(AudioCommand::SetVolume(clamped_volume))?;
        Ok(())
    }
}
