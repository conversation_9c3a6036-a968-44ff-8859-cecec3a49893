{"rustc": 10895048813736897673, "features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"events\", \"windows\"]", "declared_features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"event-stream\", \"events\", \"filedescriptor\", \"libc\", \"osc52\", \"serde\", \"use-dev-tty\", \"windows\"]", "target": 7162149947039624270, "profile": 2241668132362809309, "path": 427690204101721828, "deps": [[4495526598637097934, "parking_lot", false, 3767680566235366959], [7896293946984509699, "bitflags", false, 15905395958455809269], [10020888071089587331, "<PERSON>ap<PERSON>", false, 5629198755280755023], [11293676373856528358, "derive_more", false, 3000900537960009707], [11763018104473073732, "document_features", false, 665916809547510706], [17658759660230624279, "crossterm_winapi", false, 5115964977227050087]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\crossterm-b8d6793bd2994174\\dep-lib-crossterm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}