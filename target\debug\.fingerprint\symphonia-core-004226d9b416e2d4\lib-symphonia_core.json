{"rustc": 10895048813736897673, "features": "[\"default\"]", "declared_features": "[\"default\", \"opt-simd\", \"opt-simd-avx\", \"opt-simd-neon\", \"opt-simd-sse\", \"rustfft\"]", "target": 18060829941030025544, "profile": 2241668132362809309, "path": 5309126760894215592, "deps": [[5986029879202738730, "log", false, 8328376911722723733], [6511429716036861196, "bytemuck", false, 1606424755971263863], [10435729446543529114, "bitflags", false, 6341912448278852520], [13847662864258534762, "arrayvec", false, 7204368879174809943], [17917672826516349275, "lazy_static", false, 1693215947347874771]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\symphonia-core-004226d9b416e2d4\\dep-lib-symphonia_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}